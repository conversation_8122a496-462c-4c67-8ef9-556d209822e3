import { DBOS } from "@dbos-inc/dbos-sdk";
import { ComplianceReport, ComplianceViolation, RegulatoryUpdate } from '../types';
import { ComplianceDatabase } from '../database';

export class ReportGeneration {
  
  // Regulatory Monitoring Steps
  @DBOS.step()
  static async fetchRegulatoryUpdates(): Promise<RegulatoryUpdate[]> {
    DBOS.logger.info('Fetching latest regulatory updates');

    // Simulate fetching from regulatory websites/APIs
    await DBOS.sleep(2000);

    // Get regulatory updates from database
    const updates = await ReportGeneration.getRegulatoryUpdatesTransaction();

    DBOS.logger.info(`Fetched ${updates.length} regulatory updates from database`);
    return updates;
  }

  @DBOS.step()
  static async analyzeRegulatoryImpact(updates: RegulatoryUpdate[]): Promise<string[]> {
    DBOS.logger.info('Analyzing regulatory impact');
    
    await DBOS.sleep(1000);
    
    const recommendations: string[] = [];
    
    for (const update of updates) {
      if (update.actionRequired) {
        switch (update.impact) {
          case 'high':
            recommendations.push(`URGENT: Review and update policies for ${update.title}`);
            recommendations.push(`URGENT: Train compliance team on ${update.standard} changes`);
            break;
          case 'medium':
            recommendations.push(`PRIORITY: Update procedures for ${update.title}`);
            break;
          case 'low':
            recommendations.push(`MONITOR: Track implementation of ${update.title}`);
            break;
        }
      }
    }
    
    DBOS.logger.info(`Generated ${recommendations.length} recommendations`);
    return recommendations;
  }

  // Report Generation Step (calls database transaction)
  @DBOS.step()
  static async generateComplianceMetrics(standards?: string[]): Promise<{
    totalDocuments: number;
    compliantDocuments: number;
    violationsCount: number;
    complianceRate: number;
  }> {
    DBOS.logger.info(`Generating compliance metrics${standards ? ` for standards: ${standards.join(', ')}` : ''}`);

    // Get real metrics from database (with optional standards filtering)
    const metrics = await ReportGeneration.getComplianceMetricsTransaction(standards);

    DBOS.logger.info(`Generated metrics: ${metrics.totalDocuments} total documents, ${metrics.complianceRate}% compliance rate`);

    return metrics;
  }

  @DBOS.step()
  static async formatComplianceReport(
    metrics: {
      totalDocuments: number;
      compliantDocuments: number;
      violationsCount: number;
      complianceRate: number;
    },
    violations: ComplianceViolation[],
    recommendations: string[],
    timePeriod: 'monthly' | 'quarterly' | 'annual' = 'monthly',
    reportType?: string,
    dateRange?: { startDate: string; endDate: string }
  ): Promise<ComplianceReport> {
    DBOS.logger.info(`Formatting compliance report - Type: ${reportType || 'default'}, Period: ${timePeriod}`);

    // Generate report ID with type prefix
    const typePrefix = reportType ? reportType.toUpperCase().replace('-', '') : 'RPT';
    const report: ComplianceReport = {
      id: `${typePrefix}-${Date.now()}`,
      reportType: timePeriod,
      generatedAt: new Date(),
      compliance_rate: metrics.complianceRate,
      violations: violations.slice(0, 10), // Top 10 violations
      recommendations
    };

    // Save report to database
    await ReportGeneration.saveComplianceReportTransaction(report);

    DBOS.logger.info(`Compliance report ${report.id} formatted and saved to database`);
    return report;
  }

  // Database transaction methods (called by steps)
  @DBOS.transaction()
  static async getRegulatoryUpdatesTransaction(): Promise<RegulatoryUpdate[]> {
    return await ComplianceDatabase.getRegulatoryUpdates();
  }

  @DBOS.transaction()
  static async getComplianceMetricsTransaction(standards?: string[]): Promise<{
    totalDocuments: number;
    compliantDocuments: number;
    violationsCount: number;
    complianceRate: number;
  }> {
    return await ComplianceDatabase.getComplianceMetrics(standards);
  }

  @DBOS.transaction()
  static async saveComplianceReportTransaction(report: ComplianceReport): Promise<void> {
    return await ComplianceDatabase.saveComplianceReport(report);
  }
}
